{% extends "ecom/Admin/admin_base.html" %}

{% block title %}Orders List{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
{% endblock %}

{% block content %}
<div class="container-fluid py-1">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Orders</h1>
        <a href="{% url 'create_order' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> New Order
        </a>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">Filters</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-2">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ filters.start_date }}">
                </div>
                <div class="col-md-2">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ filters.end_date }}">
                </div>
                <div class="col-md-2">
                    <label for="customer" class="form-label">Customer</label>
                    <select class="form-control select2" id="customer" name="customer">
                        <option value="">All Customers</option>
                        {% for customer in customers %}
                            <option value="{{ customer.id }}" {% if filters.customer == customer.id|stringformat:"i" %}selected{% endif %}>
                                {{ customer }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-control" id="status" name="status">
                        <option value="">All Statuses</option>
                        {% for status_code, status_name in status_choices %}
                            {% if status_code %}
                                <option value="{{ status_code }}" {% if filters.status == status_code %}selected{% endif %}>
                                    {{ status_name }}
                                </option>
                            {% endif %}
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="invoice" class="form-label">Invoice #</label>
                    <input type="text" class="form-control" id="invoice" name="invoice" value="{{ filters.invoice }}">
                </div>
                <div class="col-md-2">
                    <label for="product" class="form-label">Product</label>
                    <select class="form-control select2" id="product" name="product">
                        <option value="">All Products</option>
                        {% for product in products %}
                            <option value="{{ product.id }}" {% if filters.product == product.id|stringformat:"i" %}selected{% endif %}>
                                {{ product.item }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">Apply Filters</button>
                    <a href="{% url 'order_list' %}" class="btn btn-outline-secondary">Clear Filters</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Orders List -->
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Orders List</h5>
        </div>
        <div class="card-body">
            {% if orders %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Invoice #</th>
                                <th>Date</th>
                                <th>Customer</th>
                                <th>PO #</th>
                                <th>Total Amount</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in orders %}
                                <tr>
                                    <td>{{ order.get_invoice }}</td>
                                    <td>{{ order.date|date:"d/m/Y" }}</td>
                                    <td>{{ order.customer }}</td>
                                    <td>{% if order.po %}{{ order.po }}{% else %}<span class="text-muted">—</span>{% endif %}</td>
                                    <td>₹{{ order.amount|default:"0.00" }}</td>
                                    <td>
                                        {% if order.status == 'Pending' %}
                                            <span class="badge bg-warning text-dark">{{ order.status }}</span>
                                        {% elif order.status == 'Delivered' %}
                                            <span class="badge bg-info">{{ order.status }}</span>
                                        {% elif order.status == 'Payment Complete' %}
                                            <span class="badge bg-success">{{ order.status }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ order.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'order_detail' order.id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'update_order' order.id %}" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if order.status == 'Delivered' or order.status == 'Payment Complete' %}
                                                <a href="{% url 'order_toggle_payment_complete' order.id %}" class="btn btn-sm btn-outline-{% if order.status == 'Payment Complete' %}warning{% else %}success{% endif %}"
                                                   title="{% if order.status == 'Payment Complete' %}Mark as Delivered{% else %}Mark as Payment Complete{% endif %}">
                                                    <i class="fas fa-{% if order.status == 'Payment Complete' %}undo{% else %}check{% endif %}"></i>
                                                </a>
                                            {% endif %}
                                            {% if order.status != 'Delivered' and order.status != 'Payment Complete' %}
                                                <form action="{% url 'delete-order' order.id %}" method="post" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this order? This action cannot be undone.')">
                                                    {% csrf_token %}
                                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

            {% else %}
                <div class="alert alert-info">
                    No orders found based on your search criteria.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize Select2 for customer selector
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });

        // Initialize Select2 for product selector with enhanced search
        $('#product').select2({
            theme: 'bootstrap-5',
            width: '100%',
            placeholder: 'Search for a product...',
            allowClear: true,
            minimumInputLength: 1,
            matcher: function(params, data) {
                // If there are no search terms, return all of the data
                if ($.trim(params.term) === '') {
                    return data;
                }

                // Do not display the item if there is no 'text' property
                if (typeof data.text === 'undefined') {
                    return null;
                }

                // `params.term` should be the term that is used for searching
                // `data.text` is the text that is displayed for the data object
                if (data.text.toLowerCase().indexOf(params.term.toLowerCase()) > -1) {
                    return data;
                }

                // Return `null` if the term should not be displayed
                return null;
            }
        });
    });
</script>
{% endblock %}